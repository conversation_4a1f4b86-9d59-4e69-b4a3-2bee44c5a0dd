/**
 * Todo 任务管理模块
 * 负责任务的 CRUD 操作和查询功能
 */

const { API_CONFIG, TASK_CONFIG, ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  formatDateForApi,
  simplifyTaskData,
  removeEmptyFields,
} = require('./utils')

/**
 * 任务管理类
 */
class TaskManager {
  constructor(authManager) {
    this.authManager = authManager
    console.log('[TaskManager] 任务管理器初始化完成')
  }

  /**
   * 获取任务列表
   * @param {object} options - 查询参数
   * @returns {object} 任务列表
   */
  async getTasks(options = {}) {
    const { mode = 'all', keyword = null, priority = null, projectName = null, completed = null } = options

    console.log('[TaskManager] [getTasks] 开始获取任务列表，过滤条件：', {
      mode,
      keyword,
      priority,
      projectName,
      completed,
    })

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager] [getTasks] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { tasks, projects } = batchResult.data
      let filteredTasks = []
      console.log(`[TaskManager] [getTasks] 原始任务数：${tasks.length}, 项目数：${projects.length}`)

      // 处理时间筛选
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

      for (const task of tasks) {
        // 只处理文本类型的任务
        if (task.kind !== TASK_CONFIG.KIND.TEXT) continue

        // 时间筛选
        if (mode !== TASK_CONFIG.FILTER_MODE.ALL) {
          const taskDate = task.modifiedTime ? new Date(task.modifiedTime) : null
          if (!taskDate) continue

          const taskDay = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate())

          if (mode === TASK_CONFIG.FILTER_MODE.TODAY && taskDay.getTime() !== today.getTime()) continue
          if (mode === TASK_CONFIG.FILTER_MODE.YESTERDAY && taskDay.getTime() !== yesterday.getTime()) continue
          if (mode === TASK_CONFIG.FILTER_MODE.RECENT_7_DAYS && taskDay < sevenDaysAgo) continue
        }

        // 完成状态筛选
        if (completed !== null) {
          const isCompleted = task.status === TASK_CONFIG.STATUS.COMPLETED || task.isCompleted
          if (completed !== isCompleted) continue
        }

        // 优先级筛选
        if (priority !== null && task.priority !== priority) continue

        // 关键词筛选
        if (keyword) {
          const searchText = `${task.title || ''} ${task.content || ''}`.toLowerCase()
          if (!searchText.includes(keyword.toLowerCase())) continue
        }

        // 项目名称筛选
        if (projectName) {
          const project = projects.find((p) => p.id === task.projectId)
          if (!project || !project.name.toLowerCase().includes(projectName.toLowerCase())) continue
        }

        // 简化任务数据
        const simplifiedTask = simplifyTaskData(task)

        // 添加项目信息
        if (task.projectId) {
          const project = projects.find((p) => p.id === task.projectId)
          if (project) {
            simplifiedTask.projectName = project.name
            simplifiedTask.projectColor = project.color
          }
        }

        filteredTasks.push(simplifiedTask)
      }

      console.log(`[TaskManager] [getTasks] 筛选后任务数：${filteredTasks.length}`)
      return createSuccessResponse('获取任务列表成功', filteredTasks)
    } catch (error) {
      console.error('[TaskManager] [getTasks] 获取任务列表异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务列表失败', error)
    }
  }

  /**
   * 创建任务
   * @param {object} options - 任务数据
   * @returns {object} 创建结果
   */
  async createTask(options = {}) {
    // 使用安全的参数处理方式，类似 Python 版本
    const {
      title = null,
      content = null,
      priority = null,
      projectName = null,
      tagNames = null,
      startDate = null,
      dueDate = null,
      isAllDay = null,
      reminder = null,
      kind = null,
    } = options

    console.log(`[TaskManager] [createTask] 开始创建任务，标题：${title}`)

    // 参数校验
    const validation = validateParams({ title }, ['title'])
    if (validation) {
      console.warn('[TaskManager] [createTask] 参数校验失败：', validation)
      return validation
    }

    try {
      // 获取基础数据以查找项目和标签
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager] [createTask] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects, tags } = batchResult.data
      let projectId = null
      let tagIds = []

      // 查找项目 ID（如果指定了项目名称）
      if (projectName !== null) {
        if (projectName) {
          const project = projects.find((p) => p.name.toLowerCase() === projectName.toLowerCase())
          if (project) {
            projectId = project.id
          } else {
            console.error(`[TaskManager] [createTask] 未找到项目：${projectName}`)
            return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到项目：${projectName}`)
          }
        }
      }

      // 查找标签 ID（如果指定了标签名称）
      if (tagNames !== null) {
        if (tagNames && Array.isArray(tagNames) && tagNames.length > 0) {
          for (const tagName of tagNames) {
            const tag = tags.find((t) => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn(`[TaskManager] [createTask] 未找到标签：${tagName}`)
            }
          }
        }
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeTaskData = {
        title: title,
        content: content !== null ? content : '',
        priority: priority !== null ? priority : 0,
        kind: kind !== null ? kind : 'TEXT',
        status: TASK_CONFIG.STATUS.ACTIVE,
        isAllDay: isAllDay !== null ? isAllDay : false,
      }

      // 添加可选字段（只有在明确指定时才添加）
      if (projectId !== null) safeTaskData.projectId = projectId
      if (tagIds.length > 0) safeTaskData.tags = tagIds
      if (startDate !== null) safeTaskData.startDate = startDate ? formatDateForApi(startDate) : null
      if (dueDate !== null) safeTaskData.dueDate = dueDate ? formatDateForApi(dueDate) : null
      if (reminder !== null) safeTaskData.reminder = reminder

      // 移除空值字段
      const cleanTaskData = removeEmptyFields(safeTaskData)
      console.log('[TaskManager] [createTask] 准备发送的任务数据：', cleanTaskData)

      // 发送创建请求
      const result = await this.authManager._request('POST', API_CONFIG.TASK_URL, cleanTaskData)
      if (result.errCode) {
        console.error('[TaskManager] [createTask] 创建任务失败：', result)
        return result
      }

      console.log('[TaskManager] [createTask] 任务创建成功')
      return createSuccessResponse('任务创建成功', result.data)
    } catch (error) {
      console.error('[TaskManager] [createTask] 创建任务异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建任务失败', error)
    }
  }

  /**
   * 获取单个任务详情
   * @param {string} taskId - 任务 ID
   * @returns {object} 任务详情
   */
  async getTask(taskId) {
    console.log(`[TaskManager] [getTask] 开始获取任务详情，ID: ${taskId}`)

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager] [getTask] 参数校验失败：', validation)
      return validation
    }

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager] [getTask] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { tasks } = batchResult.data
      const task = tasks.find((t) => t.id === taskId)

      if (!task) {
        console.warn(`[TaskManager] [getTask] 未找到任务：${taskId}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, '任务不存在')
      }

      // 简化任务数据
      const simplifiedTask = simplifyTaskData(task)

      console.log('[TaskManager] [getTask] 获取任务详情成功')
      return createSuccessResponse('获取任务详情成功', simplifiedTask)
    } catch (error) {
      console.error('[TaskManager] [getTask] 获取任务详情异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务详情失败', error)
    }
  }

  /**
   * 更新任务
   * @param {string} taskId - 任务 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateTask(taskId, updateData) {
    console.log(`[TaskManager] [updateTask] 开始更新任务，ID: ${taskId}`, {
      updateData,
    })

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager] [updateTask] 参数校验失败：', validation)
      return validation
    }

    try {
      // 首先获取现有任务数据
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager] [updateTask] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { tasks, projects, tags } = batchResult.data

      // 查找现有任务
      const existingTask = tasks.find((t) => t.id === taskId)
      if (!existingTask) {
        console.error(`[TaskManager] [updateTask] 未找到任务：${taskId}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, `未找到任务：${taskId}`)
      }

      // 处理项目名称转换为项目 ID
      let projectId = updateData.projectId
      if (updateData.projectName !== undefined) {
        if (updateData.projectName) {
          const project = projects.find((p) => p.name.toLowerCase() === updateData.projectName.toLowerCase())
          if (project) {
            projectId = project.id
          } else {
            console.error(`[TaskManager] [updateTask] 未找到项目：${updateData.projectName}`)
            return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到项目：${updateData.projectName}`)
          }
        } else {
          projectId = null
        }
      }

      // 处理标签名称转换为标签 ID
      let tagIds = updateData.tags
      if (updateData.tagNames !== undefined) {
        if (updateData.tagNames && Array.isArray(updateData.tagNames)) {
          tagIds = []
          for (const tagName of updateData.tagNames) {
            const tag = tags.find((t) => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn(`[TaskManager] [updateTask] 未找到标签：${tagName}`)
            }
          }
        } else {
          tagIds = []
        }
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeUpdateData = {
        id: taskId,
        title: updateData.title !== undefined ? updateData.title : existingTask.title,
        content: updateData.content !== undefined ? updateData.content : existingTask.content,
        priority: updateData.priority !== undefined ? updateData.priority : existingTask.priority,
        projectId: projectId !== undefined ? projectId : existingTask.projectId,
        tags: tagIds !== undefined ? tagIds : existingTask.tags || [],
        isAllDay: updateData.isAllDay !== undefined ? updateData.isAllDay : existingTask.isAllDay,
        status: updateData.status !== undefined ? updateData.status : existingTask.status,
        kind: updateData.kind !== undefined ? updateData.kind : existingTask.kind,
      }

      // 处理日期字段
      if (updateData.startDate !== undefined) {
        safeUpdateData.startDate = updateData.startDate ? formatDateForApi(updateData.startDate) : null
      } else if (existingTask.startDate) {
        safeUpdateData.startDate = existingTask.startDate
      }

      if (updateData.dueDate !== undefined) {
        safeUpdateData.dueDate = updateData.dueDate ? formatDateForApi(updateData.dueDate) : null
      } else if (existingTask.dueDate) {
        safeUpdateData.dueDate = existingTask.dueDate
      }

      // 处理提醒字段
      if (updateData.reminder !== undefined) {
        safeUpdateData.reminder = updateData.reminder
      } else if (existingTask.reminder) {
        safeUpdateData.reminder = existingTask.reminder
      }

      // 处理完成时间字段
      if (updateData.completedTime !== undefined) {
        safeUpdateData.completedTime = updateData.completedTime
      } else if (existingTask.completedTime) {
        safeUpdateData.completedTime = existingTask.completedTime
      }

      // 移除空值字段
      const cleanUpdateData = removeEmptyFields(safeUpdateData)
      console.log('[TaskManager] [updateTask] 准备发送的更新数据：', cleanUpdateData)

      // 发送更新请求
      const result = await this.authManager._request('POST', `${API_CONFIG.TASK_URL}/${taskId}`, cleanUpdateData)
      if (result.errCode) {
        console.error('[TaskManager] [updateTask] 更新任务失败：', result)
        return result
      }

      console.log('[TaskManager] [updateTask] 任务更新成功')
      return createSuccessResponse('任务更新成功', result.data)
    } catch (error) {
      console.error('[TaskManager] [updateTask] 更新任务异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新任务失败', error)
    }
  }

  /**
   * 删除任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 删除结果
   */
  async deleteTask(taskId) {
    console.log(`[TaskManager] [deleteTask] 开始删除任务，ID: ${taskId}`)

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager] [deleteTask] 参数校验失败：', validation)
      return validation
    }

    try {
      // 发送删除请求
      const result = await this.authManager._request('DELETE', `${API_CONFIG.TASK_URL}/${taskId}`)
      if (result.errCode) {
        console.error('[TaskManager] [deleteTask] 删除任务失败：', result)
        return result
      }

      console.log('[TaskManager] [deleteTask] 任务删除成功')
      return createSuccessResponse('任务删除成功', result.data)
    } catch (error) {
      console.error('[TaskManager] [deleteTask] 删除任务异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除任务失败', error)
    }
  }
}

module.exports = TaskManager
